/**
 * 物料管理服务层
 * 处理物料相关的业务逻辑
 */

const databaseManager = require('../../../database/database');
const logger = require('../../../utils/logger');
// 延迟加载避免循环依赖
let QRCodeService, TransactionService;

class MaterialsService {
    constructor() {
        this.db = databaseManager.getConnection();
    }

    /**
     * 获取物料列表 - 优化版本，避免N+1查询
     */
    async getMaterials(filters = {}) {
        try {
            // 优化：使用单个查询获取物料和库存信息，避免N+1问题
            let query = `
                SELECT m.*,
                       COALESCE(stock_summary.current_stock, 0) as current_stock,
                       COALESCE(stock_summary.total_inbound, 0) as total_inbound,
                       COALESCE(stock_summary.total_outbound, 0) as total_outbound,
                       COALESCE(stock_summary.total_return, 0) as total_return
                FROM warehouse_materials m
                LEFT JOIN (
                    SELECT
                        item_id,
                        SUM(CASE WHEN transaction_type = 'inbound' THEN quantity ELSE 0 END) as total_inbound,
                        SUM(CASE WHEN transaction_type = 'outbound' THEN quantity ELSE 0 END) as total_outbound,
                        SUM(CASE WHEN transaction_type = 'return' THEN quantity ELSE 0 END) as total_return,
                        SUM(CASE WHEN transaction_type = 'inbound' THEN quantity ELSE 0 END) -
                        SUM(CASE WHEN transaction_type = 'outbound' THEN quantity ELSE 0 END) +
                        SUM(CASE WHEN transaction_type = 'return' THEN quantity ELSE 0 END) as current_stock
                    FROM warehouse_inventory_transactions
                    WHERE item_type = 'material'
                    GROUP BY item_id
                ) stock_summary ON m.id = stock_summary.item_id
                WHERE 1=1
            `;

            const params = [];

            if (filters.material_type) {
                query += ' AND m.material_type = ?';
                params.push(filters.material_type);
            }

            if (filters.status) {
                query += ' AND m.status = ?';
                params.push(filters.status);
            }
            
            if (filters.search) {
                query += ' AND (m.material_name LIKE ? OR m.material_code LIKE ?)';
                params.push(`%${filters.search}%`, `%${filters.search}%`);
            }
            
            query += ' GROUP BY m.id ORDER BY m.created_at DESC';
            
            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }
            
            return this.db.prepare(query).all(params);
        } catch (error) {
            logger.error('获取物料列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取物料详情
     */
    async getMaterialById(id) {
        try {
            const material = this.db.prepare(`
                SELECT m.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_materials m
                LEFT JOIN warehouse_inventory_transactions t ON m.id = t.item_id AND t.item_type = 'material'
                WHERE m.id = ?
                GROUP BY m.id
            `).get(id);
            
            if (!material) {
                throw new Error('物料不存在');
            }
            
            return material;
        } catch (error) {
            logger.error('获取物料详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建物料
     */
    async createMaterial(materialData, operatorId) {
        try {
            const { material_code, material_name, material_type, unit, supplier_info, min_stock_level, max_stock_level, description } = materialData;
            
            // 检查物料编码是否已存在
            const existing = this.db.prepare('SELECT id FROM warehouse_materials WHERE material_code = ?').get(material_code);
            if (existing) {
                throw new Error('物料编码已存在');
            }
            
            const result = this.db.prepare(`
                INSERT INTO warehouse_materials (
                    material_code, material_name, material_type, unit, 
                    supplier_info, min_stock_level, max_stock_level, 
                    description, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, datetime('now'), datetime('now'))
            `).run(material_code, material_name, material_type, unit, supplier_info, min_stock_level, max_stock_level, description, operatorId);
            
            logger.info(`物料创建成功: ${material_code}`, { operatorId });
            return { id: result.lastInsertRowid, material_code };
        } catch (error) {
            logger.error('创建物料失败:', error);
            throw error;
        }
    }

    /**
     * 更新物料
     */
    async updateMaterial(id, materialData, operatorId) {
        try {
            const { material_name, material_type, unit, supplier_info, min_stock_level, max_stock_level, description, status } = materialData;
            
            const result = this.db.prepare(`
                UPDATE warehouse_materials 
                SET material_name = ?, material_type = ?, unit = ?, supplier_info = ?, 
                    min_stock_level = ?, max_stock_level = ?, description = ?, status = ?, 
                    updated_by = ?, updated_at = datetime('now')
                WHERE id = ?
            `).run(material_name, material_type, unit, supplier_info, min_stock_level, max_stock_level, description, status, operatorId, id);
            
            if (result.changes === 0) {
                throw new Error('物料不存在或更新失败');
            }
            
            logger.info(`物料更新成功: ID ${id}`, { operatorId });
            return { id, updated: true };
        } catch (error) {
            logger.error('更新物料失败:', error);
            throw error;
        }
    }

    /**
     * 删除物料
     */
    async deleteMaterial(id, operatorId) {
        try {
            // 检查是否有库存事务记录
            const hasTransactions = this.db.prepare('SELECT id FROM warehouse_inventory_transactions WHERE item_type = ? AND item_id = ? LIMIT 1').get('material', id);
            if (hasTransactions) {
                throw new Error('该物料存在库存事务记录，无法删除');
            }
            
            const result = this.db.prepare('DELETE FROM warehouse_materials WHERE id = ?').run(id);
            
            if (result.changes === 0) {
                throw new Error('物料不存在');
            }
            
            logger.info(`物料删除成功: ID ${id}`, { operatorId });
            return { id, deleted: true };
        } catch (error) {
            logger.error('删除物料失败:', error);
            throw error;
        }
    }

    /**
     * 物料入库
     */
    async materialInbound(inboundData, operatorId) {
        try {
            const { material_id, quantity, batch_number, qrcode, notes } = inboundData;
            
            // 验证物料存在
            const material = await this.getMaterialById(material_id);
            if (!material) {
                throw new Error('物料不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                await QRCodeService.validateQRCode(qrcode, 'material', material_id);
            }
            
            // 创建入库事务
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'inbound',
                item_type: 'material',
                item_id: material_id,
                quantity,
                batch_number,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`物料入库成功: ${material.material_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('物料入库失败:', error);
            throw error;
        }
    }

    /**
     * 物料出库
     */
    async materialOutbound(outboundData, operatorId) {
        try {
            const { material_id, quantity, qrcode, notes } = outboundData;
            
            // 验证物料存在和库存
            const material = await this.getMaterialById(material_id);
            if (!material) {
                throw new Error('物料不存在');
            }
            
            if (material.current_stock < quantity) {
                throw new Error('库存不足');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                await QRCodeService.validateQRCode(qrcode, 'material', material_id);
            }
            
            // 创建出库事务
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'outbound',
                item_type: 'material',
                item_id: material_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`物料出库成功: ${material.material_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('物料出库失败:', error);
            throw error;
        }
    }

    /**
     * 物料退料
     */
    async materialReturn(returnData, operatorId) {
        try {
            const { material_id, quantity, qrcode, notes } = returnData;
            
            // 验证物料存在
            const material = await this.getMaterialById(material_id);
            if (!material) {
                throw new Error('物料不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                await QRCodeService.validateQRCode(qrcode, 'material', material_id);
            }
            
            // 创建退料事务
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'return',
                item_type: 'material',
                item_id: material_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`物料退料成功: ${material.material_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('物料退料失败:', error);
            throw error;
        }
    }

    /**
     * 获取物料库存报告
     */
    async getMaterialsInventory(filters = {}) {
        try {
            let query = `
                SELECT m.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) as total_inbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) as total_outbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as total_return,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock,
                       CASE 
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) <= m.min_stock_level 
                           THEN 'low_stock'
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) >= m.max_stock_level 
                           THEN 'high_stock'
                           ELSE 'normal'
                       END as stock_status
                FROM warehouse_materials m
                LEFT JOIN warehouse_inventory_transactions t ON m.id = t.item_id AND t.item_type = 'material'
                WHERE m.status = 'active'
            `;
            
            const params = [];
            
            if (filters.stock_status) {
                // 这个过滤需要在HAVING子句中处理
                query += ' GROUP BY m.id HAVING stock_status = ?';
                params.push(filters.stock_status);
            } else {
                query += ' GROUP BY m.id';
            }
            
            query += ' ORDER BY m.material_name';
            
            return this.db.prepare(query).all(params);
        } catch (error) {
            logger.error('获取物料库存报告失败:', error);
            throw error;
        }
    }
}

module.exports = new MaterialsService();
