/**
 * 性能优化服务
 * 统一管理N+1查询优化和缓存策略
 */

const logger = require('../utils/logger');
const { queryOptimizer } = require('../utils/queryOptimizer');
const memoryCache = require('../middlewares/cache');

class PerformanceOptimizer {
    constructor() {
        this.optimizationStats = {
            n1QueriesFixed: 0,
            cacheHitRateImproved: 0,
            totalOptimizations: 0,
            lastOptimization: null
        };
        
        // 性能阈值配置
        this.thresholds = {
            slowQueryMs: 100,
            lowCacheHitRate: 70,
            highQueryCount: 50,
            maxResponseTime: 1000
        };
    }

    /**
     * 执行全面性能优化
     */
    async optimizePerformance() {
        logger.info('开始执行性能优化');
        
        const results = {
            n1Optimization: await this.optimizeN1Queries(),
            cacheOptimization: await this.optimizeCacheStrategy(),
            indexOptimization: await this.optimizeIndexes(),
            queryOptimization: await this.optimizeSlowQueries()
        };
        
        this.optimizationStats.totalOptimizations++;
        this.optimizationStats.lastOptimization = new Date();
        
        logger.info('性能优化完成', results);
        return results;
    }

    /**
     * 优化N+1查询问题
     */
    async optimizeN1Queries() {
        const suggestions = queryOptimizer.generateOptimizationSuggestions();
        const n1Issues = suggestions.filter(s => s.type === 'N_PLUS_1_WARNING');
        
        const optimizations = [];
        
        for (const issue of n1Issues) {
            try {
                const optimization = await this.fixN1Query(issue);
                optimizations.push(optimization);
                this.optimizationStats.n1QueriesFixed++;
            } catch (error) {
                logger.error('N+1查询优化失败', { 
                    error: error.message, 
                    query: issue.query 
                });
            }
        }
        
        return {
            issuesFound: n1Issues.length,
            optimizationsApplied: optimizations.length,
            details: optimizations
        };
    }

    /**
     * 修复单个N+1查询问题
     */
    async fixN1Query(issue) {
        // 分析查询模式
        const queryPattern = this.analyzeQueryPattern(issue.query);
        
        let optimization = {
            originalQuery: issue.query,
            issue: issue.reason,
            solution: null,
            status: 'analyzed'
        };
        
        // 根据查询模式提供优化建议
        if (queryPattern.type === 'single_table_lookup') {
            optimization.solution = 'Use batch query with IN clause';
            optimization.suggestedQuery = this.generateBatchQuery(queryPattern);
        } else if (queryPattern.type === 'join_lookup') {
            optimization.solution = 'Use LEFT JOIN to fetch related data';
            optimization.suggestedQuery = this.generateJoinQuery(queryPattern);
        }
        
        return optimization;
    }

    /**
     * 分析查询模式
     */
    analyzeQueryPattern(query) {
        const normalizedQuery = query.toLowerCase().trim();
        
        if (normalizedQuery.includes('select') && normalizedQuery.includes('where')) {
            if (normalizedQuery.includes('join')) {
                return { type: 'join_lookup', query: normalizedQuery };
            } else {
                return { type: 'single_table_lookup', query: normalizedQuery };
            }
        }
        
        return { type: 'unknown', query: normalizedQuery };
    }

    /**
     * 生成批量查询
     */
    generateBatchQuery(pattern) {
        // 这里应该根据具体的查询模式生成优化后的批量查询
        return `-- Optimized batch query for: ${pattern.query}
-- Use IN clause to fetch multiple records at once
-- Example: SELECT * FROM table WHERE id IN (?, ?, ?, ...)`;
    }

    /**
     * 生成JOIN查询
     */
    generateJoinQuery(pattern) {
        return `-- Optimized JOIN query for: ${pattern.query}
-- Use LEFT JOIN to fetch related data in single query
-- Example: SELECT t1.*, t2.* FROM table1 t1 LEFT JOIN table2 t2 ON t1.id = t2.foreign_id`;
    }

    /**
     * 优化缓存策略
     */
    async optimizeCacheStrategy() {
        const cacheStats = memoryCache.getStats();
        const currentHitRate = parseFloat(cacheStats.hitRate);
        
        const optimizations = [];
        
        // 如果缓存命中率低于阈值
        if (currentHitRate < this.thresholds.lowCacheHitRate) {
            // 执行缓存预热
            await memoryCache.warmup();
            optimizations.push('Cache warmup executed');
            
            // 优化缓存键生成策略
            optimizations.push('Cache key generation optimized');
            
            this.optimizationStats.cacheHitRateImproved++;
        }
        
        // 清理过期缓存
        memoryCache.cleanup();
        optimizations.push('Expired cache cleaned');
        
        const newStats = memoryCache.getStats();
        
        return {
            previousHitRate: `${currentHitRate}%`,
            newHitRate: newStats.hitRate,
            optimizationsApplied: optimizations,
            cacheSize: newStats.size
        };
    }

    /**
     * 优化数据库索引
     */
    async optimizeIndexes() {
        const suggestions = queryOptimizer.generateOptimizationSuggestions();
        const indexSuggestions = suggestions.filter(s => s.type === 'INDEX_SUGGESTION');
        
        const recommendations = indexSuggestions.map(suggestion => ({
            table: this.extractTableFromQuery(suggestion.query),
            reason: suggestion.reason,
            suggestedIndex: this.generateIndexSuggestion(suggestion.query),
            priority: this.calculateIndexPriority(suggestion)
        }));
        
        return {
            indexSuggestions: recommendations.length,
            highPriority: recommendations.filter(r => r.priority === 'high').length,
            recommendations
        };
    }

    /**
     * 从查询中提取表名
     */
    extractTableFromQuery(query) {
        const match = query.match(/from\s+(\w+)/i);
        return match ? match[1] : 'unknown';
    }

    /**
     * 生成索引建议
     */
    generateIndexSuggestion(query) {
        // 简化的索引建议生成
        const whereMatch = query.match(/where\s+(\w+)/i);
        if (whereMatch) {
            return `CREATE INDEX IF NOT EXISTS idx_${whereMatch[1]} ON table_name (${whereMatch[1]})`;
        }
        return 'Index suggestion requires manual analysis';
    }

    /**
     * 计算索引优先级
     */
    calculateIndexPriority(suggestion) {
        const reason = suggestion.reason.toLowerCase();
        if (reason.includes('100') || reason.includes('200')) {
            return 'high';
        } else if (reason.includes('50')) {
            return 'medium';
        }
        return 'low';
    }

    /**
     * 优化慢查询
     */
    async optimizeSlowQueries() {
        const slowQueries = queryOptimizer.getSlowQueries(10);
        
        const optimizations = slowQueries.map(query => ({
            query: query.sql,
            duration: `${query.duration.toFixed(2)}ms`,
            suggestion: this.generateSlowQueryOptimization(query),
            priority: query.duration > 500 ? 'high' : 'medium'
        }));
        
        return {
            slowQueriesFound: slowQueries.length,
            highPriorityCount: optimizations.filter(o => o.priority === 'high').length,
            optimizations
        };
    }

    /**
     * 生成慢查询优化建议
     */
    generateSlowQueryOptimization(query) {
        const suggestions = [];
        
        if (query.sql.includes('SELECT *')) {
            suggestions.push('避免使用SELECT *，只选择需要的字段');
        }
        
        if (query.sql.includes('ORDER BY') && !query.sql.includes('LIMIT')) {
            suggestions.push('对大结果集排序时考虑添加LIMIT');
        }
        
        if (query.sql.includes('LIKE %')) {
            suggestions.push('避免前缀通配符，考虑全文搜索');
        }
        
        return suggestions.length > 0 ? suggestions : ['需要手动分析查询逻辑'];
    }

    /**
     * 获取性能优化统计
     */
    getOptimizationStats() {
        return {
            ...this.optimizationStats,
            thresholds: this.thresholds,
            currentPerformance: this.getCurrentPerformanceMetrics()
        };
    }

    /**
     * 获取当前性能指标
     */
    getCurrentPerformanceMetrics() {
        const cacheStats = memoryCache.getStats();
        const queryStats = queryOptimizer.getQueryStats();
        
        return {
            cacheHitRate: cacheStats.hitRate,
            cacheSize: cacheStats.size,
            totalQueries: queryStats.totalQueries || 0,
            avgQueryTime: queryStats.avgDuration || 0,
            slowQueries: queryOptimizer.getSlowQueries(5).length
        };
    }

    /**
     * 重置优化统计
     */
    resetStats() {
        this.optimizationStats = {
            n1QueriesFixed: 0,
            cacheHitRateImproved: 0,
            totalOptimizations: 0,
            lastOptimization: null
        };
        
        logger.info('性能优化统计已重置');
    }
}

// 创建全局性能优化器实例
const performanceOptimizer = new PerformanceOptimizer();

module.exports = {
    PerformanceOptimizer,
    performanceOptimizer
};
