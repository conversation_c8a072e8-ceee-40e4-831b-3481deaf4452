/**
 * 认证控制器
 * 处理用户认证相关的请求
 */

const userService = require('../services/userService');

/**
 * 用户登录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function login(req, res) {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        try {
            const user = userService.authenticateUser(username, password);

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }

            const token = userService.generateToken(user);

            res.json({
                success: true,
                token,
                user
            });
        } catch (authError) {
            // 处理特定的认证错误，如账户被禁用
            console.error('认证错误:', authError);
            return res.status(403).json({
                success: false,
                message: authError.message
            });
        }
    } catch (error) {
        console.error('登录系统错误:', error);
        res.status(500).json({
            success: false,
            message: '系统错误，请稍后再试'
        });
    }
}

/**
 * 获取当前用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getCurrentUser(req, res) {
    try {
        const user = userService.getUserById(req.user.id);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 不返回密码
        const { password, ...userWithoutPassword } = user;

        // 只有admin角色拥有所有权限
        let permissions = user.permissions || [];
        const isSystemAdmin = user.role === 'admin';

        if (isSystemAdmin) {
            // 系统管理员的完整权限列表
            const adminPermissions = [
                'new_application',
                'application_record',
                'pending_approval',
                'approved_applications',
                'schedule_view',
                'schedule_create',
                'schedule_edit',
                'schedule_delete',
                'schedule_execute',
                'scheduling_manage',
                'algorithm_tuning',
                'resource_manage',
                'schedule_report',
                'product_view',
                'product_create',
                'product_edit',
                'product_delete',
                'operator_skill_manage',
                'equipment_manage',
                'equipment_info',
                'equipment_maintenance',
                'equipment_health',
                'equipment_edit',
                'quality_upload',
                'quality_view',
                'quality_download',
                'quality_manage',
                'file_upload',
                'file_view',
                'file_download',
                'file_manage',
                'file_confirm',
                'user_settings',
                'view_users',
                'create_user',
                'edit_user',
                'delete_user',
                'manage_permissions',
                'system_view',
                'system_manage',
                'warehouse_view',
                'warehouse_inbound',
                'warehouse_outbound',
                'warehouse_return',
                'warehouse_manage'
            ];

            // 合并权限并去重
            permissions = [...new Set([...permissions, ...adminPermissions])];
        }

        // 添加code和name字段
        const userWithCode = {
            ...userWithoutPassword,
            code: user.usercode,
            name: user.username,
            permissions: permissions
        };

        res.json(userWithCode);
    } catch (error) {
        console.error('获取用户信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户信息失败: ' + error.message
        });
    }
}

/**
 * 创建新用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function register(req, res) {
    try {
        const { username, password, department } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 生成用户代码
        const usercode = generateUsername(username);

        const newUser = userService.createUser({
            usercode,
            username,
            password,
            department: department || '',
            role: 'user'
        });

        // 添加code和name字段
        const userWithCode = {
            ...newUser,
            code: usercode,
            name: newUser.username
        };

        res.status(201).json({
            success: true,
            user: userWithCode
        });
    } catch (error) {
        console.error('注册失败:', error);
        res.status(500).json({
            success: false,
            message: '注册失败: ' + error.message
        });
    }
}

/**
 * 生成用户名
 * @param {string} name - 用户姓名
 * @returns {string} 生成的用户名
 */
function generateUsername(name) {
    const timestamp = Date.now().toString().slice(-6);
    return `user${timestamp}`;
}

module.exports = {
    login,
    getCurrentUser,
    register
};
