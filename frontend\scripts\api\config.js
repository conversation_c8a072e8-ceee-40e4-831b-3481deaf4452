/**
 * API配置和全局错误处理
 * 包含axios拦截器、统一的错误处理逻辑和缓存机制
 */

import configManager from '../configManager.js';

// API基础URL - 从配置管理器动态获取
export const getApiUrl = () => configManager.getApiUrl();

// 兼容性：保留原有的API_URL导出
export let API_URL = `${window.location.protocol}//${window.location.hostname}:3000/api`;

// API缓存配置
const CACHE_CONFIG = {
    // 缓存时间配置（毫秒）
    TTL: {
        SHORT: 30 * 1000,      // 30秒 - 用于频繁变化的数据
        MEDIUM: 5 * 60 * 1000, // 5分钟 - 用于中等频率变化的数据
        LONG: 30 * 60 * 1000   // 30分钟 - 用于相对稳定的数据
    },
    // 最大缓存条目数
    MAX_ENTRIES: 100
};

// 内存缓存实现
class APICache {
    constructor() {
        this.cache = new Map();
        this.timers = new Map();
    }

    /**
     * 生成缓存键
     */
    generateKey(url, params = {}) {
        const sortedParams = Object.keys(params)
            .sort()
            .reduce((result, key) => {
                result[key] = params[key];
                return result;
            }, {});
        return `${url}?${JSON.stringify(sortedParams)}`;
    }

    /**
     * 设置缓存
     */
    set(key, data, ttl = CACHE_CONFIG.TTL.MEDIUM) {
        // 如果缓存已满，删除最旧的条目
        if (this.cache.size >= CACHE_CONFIG.MAX_ENTRIES) {
            const firstKey = this.cache.keys().next().value;
            this.delete(firstKey);
        }

        // 设置缓存数据
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });

        // 设置过期定时器
        const timer = setTimeout(() => {
            this.delete(key);
        }, ttl);

        this.timers.set(key, timer);
    }

    /**
     * 获取缓存
     */
    get(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        // 检查是否过期
        if (Date.now() - cached.timestamp > cached.ttl) {
            this.delete(key);
            return null;
        }

        // 更新访问时间和计数
        cached.lastAccessed = Date.now();
        cached.accessCount = (cached.accessCount || 0) + 1;

        return cached.data;
    }

    /**
     * 获取过期缓存（降级使用）
     */
    getStale(key) {
        const cached = this.cache.get(key);
        return cached ? cached.data : null;
    }

    /**
     * 记录缓存命中
     */
    recordHit(key) {
        this.stats.hits = (this.stats.hits || 0) + 1;
    }

    /**
     * 记录缓存未命中
     */
    recordMiss(key) {
        this.stats.misses = (this.stats.misses || 0) + 1;
    }

    /**
     * 获取缓存统计
     */
    getStats() {
        const total = (this.stats.hits || 0) + (this.stats.misses || 0);
        const hitRate = total > 0 ? ((this.stats.hits || 0) / total * 100).toFixed(2) : 0;

        return {
            hits: this.stats.hits || 0,
            misses: this.stats.misses || 0,
            hitRate: `${hitRate}%`,
            size: this.cache.size,
            maxSize: CACHE_CONFIG.MAX_ENTRIES
        };
    }

    /**
     * 预热缓存
     */
    async warmup(endpoints = []) {
        console.log('开始前端缓存预热', { endpointCount: endpoints.length });

        const defaultEndpoints = [
            '/api/users/me',
            '/api/applications?status=pending',
            '/api/equipment?status=active'
        ];

        const allEndpoints = [...defaultEndpoints, ...endpoints];

        for (const endpoint of allEndpoints) {
            try {
                // 直接使用axios避免循环引用
                const response = await axios.get(endpoint);
                if (response.data) {
                    this.set(this.generateKey(endpoint, {}), response.data, CACHE_CONFIG.TTL.MEDIUM);
                }
            } catch (error) {
                console.warn('缓存预热失败:', endpoint, error);
            }
        }

        console.log('前端缓存预热完成', this.getStats());
    }

    /**
     * 删除缓存
     */
    delete(key) {
        this.cache.delete(key);
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
    }

    /**
     * 清空缓存
     */
    clear() {
        this.cache.clear();
        this.timers.forEach(timer => clearTimeout(timer));
        this.timers.clear();
    }

    /**
     * 获取缓存统计信息
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: CACHE_CONFIG.MAX_ENTRIES,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 创建全局缓存实例
export const apiCache = new APICache();

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
export function getAuthHeaders() {
    const token = sessionStorage.getItem('authToken');
    return {
        'Authorization': `Bearer ${token}`
    };
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isAuthenticated() {
    const token = sessionStorage.getItem('authToken');
    if (!token || token === 'null' || token === 'undefined') {
        return false;
    }

    // 检查token是否过期（简单的客户端检查）
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        if (payload.exp && payload.exp < currentTime) {
            // Token已过期，清除认证信息
            console.warn('Token已过期，清除认证信息');
            clearAuth();
            return false;
        }

        return true;
    } catch (error) {
        // Token格式无效，清除认证信息
        console.warn('Token格式无效，清除认证信息');
        clearAuth();
        return false;
    }
}

/**
 * 清除认证信息
 */
export function clearAuth() {
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('currentUser');
}

/**
 * 重定向到登录页面
 * @param {string} message - 可选的提示信息
 */
export function redirectToLogin(message = '请先登录') {
    // 清除认证信息
    clearAuth();

    // 保存当前页面URL，登录后可以返回
    const currentPath = window.location.pathname + window.location.search;
    if (currentPath !== '/login' && currentPath !== '/') {
        sessionStorage.setItem('redirectAfterLogin', currentPath);
    }

    // 显示提示信息
    if (message) {
        // 创建临时提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // 3秒后自动移除提示
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    // 延迟重定向，让用户看到提示信息
    setTimeout(() => {
        window.location.href = '/login';
    }, 1000);
}

// 请求去重映射
const requestMap = new Map();

/**
 * 生成请求唯一标识
 */
function generateRequestKey(config) {
    const { method, url, params, data } = config;
    return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`;
}

/**
 * 设置axios全局拦截器
 */
export function setupAxiosInterceptors() {
    // 请求拦截器 - 自动添加认证头、性能监控
    axios.interceptors.request.use(
        (config) => {
            // 添加请求开始时间
            config.metadata = { startTime: Date.now() };

            // 如果请求需要认证且用户已登录，自动添加认证头
            const token = sessionStorage.getItem('authToken');
            if (token && token !== 'null' && token !== 'undefined') {
                config.headers.Authorization = `Bearer ${token}`;
            }

            // 注意：移除了有问题的请求去重逻辑，避免递归调用和Promise返回类型错误
            // 请求去重应该在更高层级处理，而不是在axios拦截器中

            return config;
        },
        (error) => {
            console.error('请求拦截器错误:', error);
            return Promise.reject(error);
        }
    );

    // 响应拦截器 - 处理认证错误、性能监控
    axios.interceptors.response.use(
        (response) => {
            // 计算请求耗时
            if (response.config.metadata) {
                const duration = Date.now() - response.config.metadata.startTime;

                // 记录慢请求
                if (duration > 2000) {
                    console.warn(`慢请求检测: ${response.config.method.toUpperCase()} ${response.config.url} - ${duration}ms`);
                }

                // 添加性能信息到响应头
                response.duration = duration;
            }

            return response;
        },
        (error) => {
            // 计算请求耗时（即使失败）
            if (error.config && error.config.metadata) {
                const duration = Date.now() - error.config.metadata.startTime;
                error.duration = duration;
            }

            // 处理网络错误
            if (!error.response) {
                console.error('网络错误:', error.message);
                return Promise.reject(error);
            }

            const { status, data } = error.response;

            switch (status) {
                case 401:
                    // 未认证 - 重定向到登录页面
                    console.warn('用户未认证，重定向到登录页面');
                    redirectToLogin('登录已过期，请重新登录');
                    break;

                case 403:
                    // 权限不足或token无效
                    if (data.message && (data.message.includes('令牌') || data.message.includes('过期'))) {
                        // token相关错误 - 重定向到登录页面
                        console.warn('Token无效或已过期，重定向到登录页面');
                        redirectToLogin('登录已过期，请重新登录');
                    } else {
                        // 权限不足 - 显示错误信息但不重定向
                        console.warn('权限不足:', data.message);
                    }
                    break;

                case 404:
                    console.error('请求的资源不存在');
                    break;

                case 500:
                    console.error('服务器内部错误');
                    break;

                case 429:
                    console.warn('请求过于频繁，请稍后再试');
                    break;

                default:
                    console.error(`请求失败 (${status}):`, data.message || error.message);
            }

            return Promise.reject(error);
        }
    );
}

// 请求防抖映射
const pendingRequests = new Map();

/**
 * 防抖请求函数
 */
export function debounceRequest(key, requestFn, delay = 300) {
    return new Promise((resolve, reject) => {
        // 取消之前的请求
        if (pendingRequests.has(key)) {
            clearTimeout(pendingRequests.get(key).timer);
        }

        // 设置新的延迟请求
        const timer = setTimeout(async () => {
            try {
                const result = await requestFn();
                pendingRequests.delete(key);
                resolve(result);
            } catch (error) {
                pendingRequests.delete(key);
                reject(error);
            }
        }, delay);

        pendingRequests.set(key, { timer, resolve, reject });
    });
}

/**
 * 带缓存的GET请求 - 优化版本
 */
export async function cachedGet(url, params = {}, options = {}) {
    const {
        ttl = CACHE_CONFIG.TTL.MEDIUM,
        useCache = true,
        forceRefresh = false,
        retryOnError = true,
        maxRetries = 2
    } = options;

    const cacheKey = apiCache.generateKey(url, params);

    // 如果不强制刷新且启用缓存，先尝试从缓存获取
    if (useCache && !forceRefresh) {
        const cached = apiCache.get(cacheKey);
        if (cached) {
            // 记录缓存命中
            apiCache.recordHit(cacheKey);
            return { data: cached, fromCache: true };
        }
    }

    let lastError;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
        try {
            // 发起请求
            const response = await axios.get(url, { params });

            // 缓存响应数据
            if (useCache && response.data && response.status === 200) {
                // 根据数据类型调整TTL
                const adjustedTTL = adjustTTLByDataType(url, response.data, ttl);
                apiCache.set(cacheKey, response.data, adjustedTTL);
            }

            // 记录缓存未命中
            apiCache.recordMiss(cacheKey);

            return { data: response.data, fromCache: false, retryCount };
        } catch (error) {
            lastError = error;
            retryCount++;

            // 如果不重试或已达到最大重试次数，抛出错误
            if (!retryOnError || retryCount > maxRetries) {
                // 尝试返回过期的缓存数据作为降级方案
                const staleCache = apiCache.getStale(cacheKey);
                if (staleCache) {
                    console.warn(`API请求失败，返回过期缓存数据: ${url}`, error);
                    return { data: staleCache, fromCache: true, isStale: true };
                }
                throw error;
            }

            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
    }

    throw lastError;
}

/**
 * 根据数据类型调整TTL
 */
function adjustTTLByDataType(url, data, defaultTTL) {
    // 用户信息缓存时间较短
    if (url.includes('/users/') || url.includes('/auth/')) {
        return CACHE_CONFIG.TTL.SHORT;
    }

    // 静态配置数据缓存时间较长
    if (url.includes('/config') || url.includes('/settings')) {
        return CACHE_CONFIG.TTL.VERY_LONG;
    }

    // 列表数据根据数量调整
    if (Array.isArray(data)) {
        return data.length > 100 ? CACHE_CONFIG.TTL.LONG : defaultTTL;
    }

    return defaultTTL;
}

/**
 * 批量请求函数
 */
export async function batchRequests(requests, options = {}) {
    const {
        concurrency = 3, // 并发数限制
        retryCount = 2,   // 重试次数
        retryDelay = 1000 // 重试延迟
    } = options;

    const results = [];
    const errors = [];

    // 分批处理请求
    for (let i = 0; i < requests.length; i += concurrency) {
        const batch = requests.slice(i, i + concurrency);

        const batchPromises = batch.map(async (request, index) => {
            const globalIndex = i + index;
            let lastError;

            // 重试机制
            for (let attempt = 0; attempt <= retryCount; attempt++) {
                try {
                    const result = await request();
                    results[globalIndex] = result;
                    return result;
                } catch (error) {
                    lastError = error;
                    if (attempt < retryCount) {
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    }
                }
            }

            errors[globalIndex] = lastError;
            return null;
        });

        await Promise.allSettled(batchPromises);
    }

    return { results, errors };
}

/**
 * 初始化API配置
 * 应在应用启动时调用
 */
export async function initializeAPI() {
    try {
        // 从配置管理器获取API URL
        const apiUrl = await configManager.getApiUrl();
        API_URL = apiUrl; // 更新全局变量

        // 设置axios默认配置
        axios.defaults.baseURL = apiUrl;
        axios.defaults.timeout = 15000; // 增加到15秒超时
        axios.defaults.headers.common['Content-Type'] = 'application/json';

        // 设置拦截器
        setupAxiosInterceptors();

        console.log('✅ API配置初始化完成');
        console.log('📡 API URL:', apiUrl);
        console.log('💾 缓存配置:', CACHE_CONFIG);
    } catch (error) {
        console.error('❌ API配置初始化失败:', error);
        // 使用默认配置继续初始化
        axios.defaults.baseURL = API_URL;
        axios.defaults.timeout = 15000;
        axios.defaults.headers.common['Content-Type'] = 'application/json';
        setupAxiosInterceptors();
    }
}
