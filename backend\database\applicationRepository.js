/**
 * 申请数据访问层
 * 使用SQLite数据库
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');

class ApplicationRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initStatements() {
        this.statements = {
            findAll: this.db.prepare(`
                SELECT * FROM applications 
                ORDER BY created_at DESC
            `),
            findById: this.db.prepare('SELECT * FROM applications WHERE id = ?'),
            findByUserId: this.db.prepare(`
                SELECT * FROM applications WHERE user_id = ? 
                ORDER BY created_at DESC
            `),
            findByStatus: this.db.prepare(`
                SELECT * FROM applications WHERE status = ? 
                ORDER BY created_at DESC
            `),
            findByStage: this.db.prepare(`
                SELECT * FROM applications WHERE current_stage = ? AND status = 'pending'
                ORDER BY created_at DESC
            `),
            insert: this.db.prepare(`
                INSERT INTO applications (
                    id, application_number, user_id, applicant, department, date,
                    content, amount, priority, type, status, current_stage,
                    need_manager_approval, need_ceo_approval, selected_factory_managers, selected_managers, pdf_path,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE applications SET
                    application_number = ?, user_id = ?, applicant = ?, department = ?,
                    date = ?, content = ?, amount = ?, priority = ?, type = ?,
                    status = ?, current_stage = ?, need_manager_approval = ?,
                    need_ceo_approval = ?, selected_factory_managers = ?, selected_managers = ?, pdf_path = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM applications WHERE id = ?'),
            
            // 附件相关
            findAttachmentsByAppId: this.db.prepare(`
                SELECT * FROM application_attachments WHERE application_id = ?
            `),
            insertAttachment: this.db.prepare(`
                INSERT INTO application_attachments (
                    id, application_id, name, path, filename, type, size, uploaded_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `),
            deleteAttachmentsByAppId: this.db.prepare(`
                DELETE FROM application_attachments WHERE application_id = ?
            `),
            findAttachmentById: this.db.prepare(`
                SELECT * FROM application_attachments WHERE id = ?
            `),
            
            // 审批历史相关
            findApprovalHistoryByAppId: this.db.prepare(`
                SELECT * FROM approval_history WHERE application_id = ? 
                ORDER BY timestamp ASC
            `),
            insertApprovalHistory: this.db.prepare(`
                INSERT INTO approval_history (
                    application_id, stage, approver_id, approver_name, approver_role,
                    action, comment, signature_path, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            deleteApprovalHistoryByAppId: this.db.prepare(`
                DELETE FROM approval_history WHERE application_id = ?
            `),
            
            // 统计查询
            countTodayApplications: this.db.prepare(`
                SELECT COUNT(*) as count FROM applications 
                WHERE DATE(created_at) = DATE(?)
            `)
        };
    }

    /**
     * 获取所有申请
     */
    findAll() {
        try {
            const applications = this.statements.findAll.all();
            return this.transformApplications(applications);
        } catch (error) {
            logger.error('获取所有申请失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找申请
     */
    findById(id) {
        try {
            const application = this.statements.findById.get(id);
            if (!application) return null;
            
            return this.transformApplication(application);
        } catch (error) {
            logger.error(`根据ID查找申请失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户ID查找申请
     */
    findByUserId(userId) {
        try {
            const applications = this.statements.findByUserId.all(userId);
            return this.transformApplications(applications);
        } catch (error) {
            logger.error(`根据用户ID查找申请失败 (${userId}):`, error);
            throw error;
        }
    }

    /**
     * 根据状态查找申请
     */
    findByStatus(status) {
        try {
            const applications = this.statements.findByStatus.all(status);
            return this.transformApplications(applications);
        } catch (error) {
            logger.error(`根据状态查找申请失败 (${status}):`, error);
            throw error;
        }
    }

    /**
     * 根据审批阶段查找申请
     */
    findByStage(stage) {
        try {
            const applications = this.statements.findByStage.all(stage);
            return this.transformApplications(applications);
        } catch (error) {
            logger.error(`根据审批阶段查找申请失败 (${stage}):`, error);
            throw error;
        }
    }

    /**
     * 创建新申请
     */
    create(applicationData) {
        const transaction = this.db.transaction(() => {
            try {
                const now = new Date().toISOString();
                
                // 插入申请主记录
                this.statements.insert.run(
                    applicationData.id,
                    applicationData.applicationNumber,
                    applicationData.userId,
                    applicationData.applicant,
                    applicationData.department || '',
                    applicationData.date,
                    applicationData.content,
                    applicationData.amount || '',
                    applicationData.priority || 'normal',
                    applicationData.type || 'standard',
                    applicationData.status || 'pending',
                    applicationData.currentStage || '',
                    applicationData.needManagerApproval ? 1 : 0,
                    applicationData.needCeoApproval ? 1 : 0,
                    JSON.stringify(applicationData.selectedFactoryManagers || []),
                    JSON.stringify(applicationData.selectedManagers || []),
                    applicationData.pdfPath || '',
                    now,
                    now
                );

                // 插入附件
                if (applicationData.attachments && applicationData.attachments.length > 0) {
                    for (const attachment of applicationData.attachments) {
                        this.statements.insertAttachment.run(
                            attachment.id,
                            applicationData.id,
                            attachment.name,
                            attachment.path,
                            attachment.filename || '',
                            attachment.type || '',
                            attachment.size || 0,
                            attachment.uploadedAt || now
                        );
                    }
                }

                return this.findById(applicationData.id);
            } catch (error) {
                logger.error('创建申请失败:', error);
                throw error;
            }
        });

        return transaction();
    }

    /**
     * 更新申请
     */
    update(id, applicationData) {
        const transaction = this.db.transaction(() => {
            try {
                const now = new Date().toISOString();
                
                // 更新申请主记录
                this.statements.update.run(
                    applicationData.applicationNumber,
                    applicationData.userId,
                    applicationData.applicant,
                    applicationData.department || '',
                    applicationData.date,
                    applicationData.content,
                    applicationData.amount || '',
                    applicationData.priority || 'normal',
                    applicationData.type || 'standard',
                    applicationData.status || 'pending',
                    applicationData.currentStage || '',
                    applicationData.needManagerApproval ? 1 : 0,
                    applicationData.needCeoApproval ? 1 : 0,
                    JSON.stringify(applicationData.selectedFactoryManagers || []),
                    JSON.stringify(applicationData.selectedManagers || []),
                    applicationData.pdfPath || '',
                    now,
                    id
                );

                // 如果有新附件，先删除旧附件再插入新附件
                if (applicationData.attachments !== undefined) {
                    this.statements.deleteAttachmentsByAppId.run(id);

                    if (applicationData.attachments.length > 0) {
                        for (const attachment of applicationData.attachments) {
                            this.statements.insertAttachment.run(
                                attachment.id,
                                id,
                                attachment.name,
                                attachment.path,
                                attachment.filename || '',
                                attachment.type || '',
                                attachment.size || 0,
                                attachment.uploadedAt || now
                            );
                        }
                    }
                }

                // 如果有新的审批历史，保存到approval_history表
                if (applicationData.approvalHistory && Array.isArray(applicationData.approvalHistory)) {
                    // 获取现有的审批历史记录数量
                    const existingHistoryCount = this.statements.findApprovalHistoryByAppId.all(id).length;

                    // 只保存新增的审批历史记录
                    const newHistoryRecords = applicationData.approvalHistory.slice(existingHistoryCount);

                    for (const history of newHistoryRecords) {
                        this.statements.insertApprovalHistory.run(
                            id,
                            history.stage,
                            history.approverId,
                            history.approverName,
                            history.approverRole,
                            history.action,
                            history.comment || '',
                            history.signaturePath || '',
                            history.timestamp
                        );
                    }
                }

                return this.findById(id);
            } catch (error) {
                logger.error(`更新申请失败 (${id}):`, error);
                throw error;
            }
        });

        return transaction();
    }

    /**
     * 删除申请
     */
    delete(id) {
        const transaction = this.db.transaction(() => {
            try {
                // 静默执行数据库删除操作

                // 删除审批历史
                const approvalResult = this.statements.deleteApprovalHistoryByAppId.run(id);

                // 删除附件记录
                const attachmentResult = this.statements.deleteAttachmentsByAppId.run(id);

                // 删除申请主记录
                const result = this.statements.delete.run(id);

                const success = result.changes > 0;

                return success;
            } catch (error) {
                logger.error(`删除申请失败 (${id}):`, error);
                throw error;
            }
        });

        return transaction();
    }

    /**
     * 添加审批历史记录
     */
    addApprovalHistory(applicationId, historyData) {
        try {
            this.statements.insertApprovalHistory.run(
                applicationId,
                historyData.stage,
                historyData.approverId,
                historyData.approverName,
                historyData.approverRole,
                historyData.action,
                historyData.comment || '',
                historyData.signaturePath || '',
                historyData.timestamp
            );
        } catch (error) {
            logger.error(`添加审批历史记录失败 (${applicationId}):`, error);
            throw error;
        }
    }

    /**
     * 获取今日申请数量
     */
    getTodayApplicationCount() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const result = this.statements.countTodayApplications.get(today);
            return result.count;
        } catch (error) {
            logger.error('获取今日申请数量失败:', error);
            throw error;
        }
    }

    /**
     * 查找附件
     */
    findAttachmentById(attachmentId) {
        try {
            return this.statements.findAttachmentById.get(attachmentId);
        } catch (error) {
            logger.error(`查找附件失败 (${attachmentId}):`, error);
            throw error;
        }
    }

    /**
     * 转换数据库申请对象为应用层对象
     * 优化：使用批量查询避免N+1问题
     */
    transformApplication(dbApplication) {
        if (!dbApplication) return null;

        // 获取附件 - 单个查询，已优化
        const attachments = this.statements.findAttachmentsByAppId.all(dbApplication.id);

        // 获取审批历史 - 单个查询，已优化
        const approvalHistory = this.statements.findApprovalHistoryByAppId.all(dbApplication.id);

        return {
            id: dbApplication.id,
            applicationNumber: dbApplication.application_number,
            userId: dbApplication.user_id,
            applicant: dbApplication.applicant,
            department: dbApplication.department,
            date: dbApplication.date,
            content: dbApplication.content,
            amount: dbApplication.amount,
            priority: dbApplication.priority,
            type: dbApplication.type,
            status: dbApplication.status,
            currentStage: dbApplication.current_stage,
            needManagerApproval: dbApplication.need_manager_approval === 1,
            needCeoApproval: dbApplication.need_ceo_approval === 1,
            selectedFactoryManagers: JSON.parse(dbApplication.selected_factory_managers || '[]'),
            selectedManagers: JSON.parse(dbApplication.selected_managers || '[]'),
            pdfPath: dbApplication.pdf_path,
            attachments: attachments.map(att => ({
                id: att.id,
                name: att.name,
                path: att.path,
                filename: att.filename,
                type: att.type,
                size: att.size,
                uploadedAt: att.uploaded_at
            })),
            approvalHistory: approvalHistory.map(hist => ({
                stage: hist.stage,
                approverId: hist.approver_id,
                approverName: hist.approver_name,
                approverRole: hist.approver_role,
                action: hist.action,
                comment: hist.comment,
                signaturePath: hist.signature_path,
                timestamp: hist.timestamp
            })),
            createdAt: dbApplication.created_at,
            updatedAt: dbApplication.updated_at
        };
    }

    /**
     * 批量转换申请对象 - 优化版本，避免N+1查询
     */
    transformApplications(dbApplications) {
        if (!dbApplications || dbApplications.length === 0) return [];

        // 批量获取所有申请的附件和审批历史
        const applicationIds = dbApplications.map(app => app.id);
        const allAttachments = this.batchFindAttachments(applicationIds);
        const allApprovalHistory = this.batchFindApprovalHistory(applicationIds);

        // 按申请ID分组
        const attachmentsByAppId = this.groupByApplicationId(allAttachments);
        const historyByAppId = this.groupByApplicationId(allApprovalHistory);

        // 转换每个申请，使用预加载的数据
        return dbApplications.map(app => this.transformApplicationWithPreloadedData(
            app,
            attachmentsByAppId[app.id] || [],
            historyByAppId[app.id] || []
        ));
    }

    /**
     * 批量获取附件数据
     */
    batchFindAttachments(applicationIds) {
        if (applicationIds.length === 0) return [];

        const placeholders = applicationIds.map(() => '?').join(',');
        const query = `
            SELECT * FROM application_attachments
            WHERE application_id IN (${placeholders})
            ORDER BY application_id, created_at
        `;

        return this.db.prepare(query).all(...applicationIds);
    }

    /**
     * 批量获取审批历史数据
     */
    batchFindApprovalHistory(applicationIds) {
        if (applicationIds.length === 0) return [];

        const placeholders = applicationIds.map(() => '?').join(',');
        const query = `
            SELECT ah.*, u.username as approver_name, u.department as approver_department
            FROM application_approval_history ah
            LEFT JOIN users u ON ah.approver_id = u.id
            WHERE ah.application_id IN (${placeholders})
            ORDER BY ah.application_id, ah.created_at
        `;

        return this.db.prepare(query).all(...applicationIds);
    }

    /**
     * 按申请ID分组数据
     */
    groupByApplicationId(items) {
        return items.reduce((groups, item) => {
            const appId = item.application_id;
            if (!groups[appId]) {
                groups[appId] = [];
            }
            groups[appId].push(item);
            return groups;
        }, {});
    }

    /**
     * 使用预加载数据转换申请对象
     */
    transformApplicationWithPreloadedData(dbApplication, attachments, approvalHistory) {
        if (!dbApplication) return null;

        return {
            id: dbApplication.id,
            applicationNumber: dbApplication.application_number,
            userId: dbApplication.user_id,
            applicant: dbApplication.applicant,
            department: dbApplication.department,
            date: dbApplication.date,
            content: dbApplication.content,
            amount: dbApplication.amount,
            priority: dbApplication.priority,
            type: dbApplication.type,
            status: dbApplication.status,
            currentStage: dbApplication.current_stage,
            needManagerApproval: dbApplication.need_manager_approval === 1,
            needCeoApproval: dbApplication.need_ceo_approval === 1,
            selectedFactoryManagers: JSON.parse(dbApplication.selected_factory_managers || '[]'),
            selectedManagers: JSON.parse(dbApplication.selected_managers || '[]'),
            pdfPath: dbApplication.pdf_path,
            attachments: attachments.map(att => ({
                id: att.id,
                name: att.name,
                path: att.path,
                filename: att.filename,
                type: att.type,
                size: att.size,
                uploadedAt: att.uploaded_at
            })),
            approvalHistory: approvalHistory.map(hist => ({
                stage: hist.stage,
                approverId: hist.approver_id,
                approverName: hist.approver_name,
                approverRole: hist.approver_role,
                action: hist.action,
                comment: hist.comment,
                signaturePath: hist.signature_path,
                timestamp: hist.timestamp
            })),
            createdAt: dbApplication.created_at,
            updatedAt: dbApplication.updated_at
        };
    }
}

module.exports = new ApplicationRepository();
